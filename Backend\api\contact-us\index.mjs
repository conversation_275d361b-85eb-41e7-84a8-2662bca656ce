/**
 * @fileoverview Contact Us form submission handler
 * This AWS Lambda function handles contact form submissions by:
 * 1. Parsing form data from the event
 * 2. Sending data to HubSpot CRM
 * 3. Sending confirmation email via SendGrid
 * 4. Sending notification to Slack
 *
 * Configuration is loaded from AWS SSM Parameter Store with fallback to environment variables.
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

import sendDataToHubspot from "../../common/sendDataToHubSpot";
import sendDataToSendGrid from "../../common/sendDataToSendGrid";
import currentTimestamp from "../../common/currentTimestamp";
import sendToSlack from "../../common/sendDataToSlack";
import { getConfigValue } from "../../common/ssmConfig";

/**
 * AWS Lambda handler for contact-us form submissions
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.body - JSON string containing form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} event.requestContext - AWS Lambda request context
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Example event.body structure:
 * {
 *   "firstName": "John",
 *   "lastName": "Doe",
 *   "emailAddress": "<EMAIL>",
 *   "phoneNumber": "+1234567890",
 *   "companyName": "Example Corp",
 *   "howCanWeHelpYou": "I need help with AI implementation",
 *   "howDidYouHearAboutUs": "Google Search",
 *   "consent": true,
 *   "url": "https://example.com/contact",
 *   "utm_source": "google",
 *   "utm_campaign": "ai-services",
 *   "utm_medium": "cpc"
 * }
 *
 * @example
 * // Success response:
 * {
 *   "statusCode": 200,
 *   "body": "{\"message\":\"Form submitted successfully.\",\"hubspotResponse\":\"Contact form data sent to HubSpot successfully.\"}"
 * }
 *
 * @example
 * // Error response:
 * {
 *   "statusCode": 500,
 *   "body": "{\"message\":\"Form submission failed.\",\"error\":\"HubSpot API error\"}"
 * }
 */
export const handler = async (event) => {
  try {
    const form_data = JSON.parse(event.body);

    const formFields = [
      { name: "firstname", value: form_data?.firstName ?? "" },
      { name: "lastname", value: form_data?.lastName ?? "" },
      { name: "email", value: form_data?.emailAddress ?? "" },
      { name: "phone", value: form_data?.phoneNumber ?? "" },
      {
        name: "how_did_you_hear_about_us_",
        value: form_data?.howDidYouHearAboutUs ?? "",
      },
      { name: "company", value: form_data?.companyName },
      { name: "message", value: form_data?.howCanWeHelpYou },
      { name: "utm_source", value: form_data?.utm_source ?? "" },
      { name: "utm_campaign", value: form_data?.utm_campaign ?? "" },
      { name: "utm_medium", value: form_data?.utm_medium ?? "" },
      { name: "clarity_link", value: form_data?.clarity ?? "" },
      { name: "source_url", value: form_data?.url ?? "" },
      { name: "referrer", value: form_data?.referrer ?? "" },
      { name: "ip_address", value: form_data?.ip_address ?? "" },
      { name: "city", value: form_data?.city ?? "" },
      { name: "country", value: form_data?.country ?? "" },
      { name: "ga_4_userid", value: form_data?.ga_4_userid },
      { name: "source", value: form_data?.secondary_source ?? "HomePage" },
      { name: "consent", value: form_data?.consent ?? "" },
    ];

    // Get configuration values from SSM
    const [hubspotApiKey, hubspotFormGuid] = await Promise.all([
      getConfigValue("HUBSPOT_API_KEY"),
      getConfigValue("HUBSPOT_GET_IN_TOUCH_FORM_GUID"),
    ]);

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${hubspotApiKey}`,
      },
    };

    try {
      // Send Data to HubSpot
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        hubspotFormGuid
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Get email configuration from SSM
        const [mailTo, mailFrom, emailTemplateId] = await Promise.all([
          getConfigValue("MAIL_TO"),
          getConfigValue("MAIL_FROM"),
          getConfigValue("SENDGRID_CONTACT_US_FORM_TEMPLATE_ID"),
        ]);

        // Send Data to SendGrid if HubSpot submission is successful
        const emailRes = await sendDataToSendGrid(
          mailTo,
          mailFrom,
          form_data?.emailAddress,
          emailTemplateId,
          form_data
        );

        // Send Data to success Slack channel (webhook URL will be determined by sendToSlack)
        await sendToSlack(form_data);

        console.log(currentTimestamp());
        console.log("Lead Data", form_data);
        console.log("HubSpot Response", hubspotResponse);
        console.log("SendGrid Email Response", emailRes);
        console.log("------------------------------------");

        return {
          statusCode: 200,
          body: JSON.stringify({
            message: "Form submitted successfully.",
            hubspotResponse: hubspotResponse.message,
          }),
        };
      } else {
        console.error("HubSpot Error:", hubspotResponse);

        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = "Hubspot";

        // Get failure email configuration from SSM
        const [failureMailTo, failureMailFrom, failureTemplateId] =
          await Promise.all([
            getConfigValue("MAIL_TO"),
            getConfigValue("MAIL_FROM"),
            getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
          ]);

        const failureEmail = await sendDataToSendGrid(
          failureMailTo,
          failureMailFrom,
          form_data?.emailAddress,
          failureTemplateId,
          formLeadData
        );

        // Send failure notification to Slack (webhook URL will be determined by sendToSlack)
        await sendToSlack(
          form_data,
          undefined, // Let sendToSlack determine the webhook URL
          "⚠️ HubSpot Form Submission Failed ⚠️"
        );

        if (failureEmail.status) {
          console.error(
            `${form_data?.secondary_source} form, failure email sent`
          );
        } else {
          console.error(
            `${form_data?.secondary_source} form, failed to send failure email`
          );
        }

        return {
          statusCode: hubspotResponse?.status || 500,
          body: JSON.stringify({
            message: "Form submission failed.",
            error: hubspotResponse?.error || "Unknown error from HubSpot",
          }),
        };
      }
    } catch (error) {
      console.error("Error sending to HubSpot:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Internal server error while sending data to HubSpot",
          error: error.message || error,
        }),
      };
    }
  } catch (error) {
    console.error("Error parsing request:", error);
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "Invalid request data",
        error: error.message || error,
      }),
    };
  }
};
